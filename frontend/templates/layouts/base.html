{{ define "base" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <title>{{ .Title }} - Omfietser</title>
    <meta name="description" content="{{ .Description }}">
    <meta name="csrf-token" content="{{ .CSRFToken }}">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#ffffff">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Omfietser">
    <meta name="app-version" content="{{ .Version }}">
    <meta name="app-environment" content="{{ .Environment }}">
    <meta name="app-build-date" content="{{ .BuildDate }}">
    {{ if .CDNEnabled }}
    <meta name="cdn-base-url" content="{{ .CDNBaseURL }}">
    {{ end }}
    <link rel="manifest" href="/app.webmanifest">

    <!-- Favicons & Apple Touch Icon -->
    <link rel="apple-touch-icon" href="{{ AssetPath "images/apple-touch-icon.png" }}">
    <link rel="icon" href="{{ AssetPath "images/favicon.svg" }}" type="image/svg+xml">
    <link rel="icon" href="{{ AssetPath "images/favicon.ico" }}" sizes="any">
    <link rel="shortcut icon" href="{{ AssetPath "images/favicon.ico" }}">

    <!-- CSS Handling -->
    {{ if .ViteDevMode }}
    <!-- In development mode with Vite, CSS is loaded through JS -->
    {{ else }}
    <!-- In production mode, load CSS if available -->
    {{ if .MainCSSPath }}
    <link rel="stylesheet" href="{{ .MainCSSPath }}">
    {{ end }}
    {{ end }}

    <!-- HTMX -->
    <script src="{{ .HTMXPath }}" defer></script>

    <!-- Main Application JS & Vite Client -->
    {{ if .ViteDevMode }}
    <!-- Development mode with Vite -->
    <script type="module" src="{{ .ViteClientSrc }}"></script>
    <script type="module" src="{{ .MainJSPath }}"></script>
    {{ else }}
    <!-- Production mode -->
    <script type="module" src="{{ .MainJSPath }}" defer></script>
    {{ end }}

    <!-- Debug info (only in development) -->
    {{ if eq .Environment "development" }}
    <script>
        console.group('Asset Debug Info');
        console.log('Environment:', '{{ .Environment }}');
        console.log('ViteDevMode:', '{{ .ViteDevMode }}');
        console.log('ViteEnabled:', '{{ .ViteEnabled }}');
        console.log('MainJSPath:', '{{ .MainJSPath }}');
        console.log('HTMXPath:', '{{ .HTMXPath }}');
        console.log('MainCSSPath:', '{{ .MainCSSPath }}');
        console.log('ViteClientSrc:', '{{ .ViteClientSrc }}');
        console.groupEnd();
    </script>
    {{ end }}
</head>
<body data-page="{{ .CurrentPage }}">
    {{ template "filter-sidebar" . }}

    <!-- Header -->
    {{ template "header" . }}

    <!-- Main Content -->
    <main class="main">
        <!-- Hidden notification area for updates -->
        <div id="product-update-notification" class="product-update-notification" role="status" aria-live="polite"></div>

        <!-- Always include product-list-container for HTMX targets -->
        <div id="product-list-container" class="product-list-wrapper">
            {{ if .Products }}
                {{ template "product-list" . }}
            {{ else if eq .ContentTemplateName "login" }}
                <!-- Login Page Content -->
                {{ template "login-content" . }}
            {{ else if eq .ContentTemplateName "signup" }}
                <!-- Signup Page Content -->
                {{ template "signup-content" . }}
            {{ else if eq .ContentTemplateName "home-component" }}
                <!-- Home Component -->
                {{ template "home-component" . }}
            {{ else }}
                <!-- Default Home Content -->
                {{ template "home-component" . }}
            {{ end }}
        </div>
    </main>

    <!-- Footer -->
    {{ template "footer" . }}
</body>
</html>
{{ end }}
