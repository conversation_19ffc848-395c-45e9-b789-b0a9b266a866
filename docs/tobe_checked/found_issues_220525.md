# Found Issues - May 22, 2025

## Investigation Status
**Current Focus**: Systematic analysis of cascading system failures post-refactoring

## Issues Identified

### 1. Template/DOM Structure Issues
**Status**: ISSUE FOUND
- Filter sidebar elements not found → toggle functionality disabled
- Products load via network but don't appear in viewport
- HTMX target containers potentially missing

**Investigation Progress**:
- [x] Check filter sidebar elements in templates
- [x] Verify HTMX target containers exist
- [ ] Compare template structure vs pre-refactoring

**FINDINGS**:
- **CRITICAL**: No `.filter-button` element in header template! JavaScript expects this to open sidebar
- **CRITICAL**: Theme toggle button exists but no `data-action="toggle-theme"` handler in JS
- **OK**: HTMX target `#product-list-container` exists in base template  
- **OK**: Filter sidebar template structure complete with proper IDs/classes
- **OK**: Header has search form and HTMX actions targeting correct containers

### 2. JavaScript Module Issues  
**Status**: MAJOR ISSUES FOUND
- Theme toggle stopped working
- Filter sidebar toggle disabled  
- Event handlers register but UI not responding

**Investigation Progress**:
- [x] Check JavaScript module dependencies
- [x] Verify DOM element selectors
- [ ] Check theme toggle implementation

**FINDINGS**:
- **CRITICAL**: sidebar_toggle.js looks for `.close-button` but template has `.filter-sidebar__close-button`
- **CRITICAL**: No `.filter-button` exists in templates - missing filter toggle button in header
- **OK**: Event handlers module loads and registers successfully
- **PARTIAL**: Theme toggle button exists in template but no theme handling in event_handlers.js

### 3. Backend API Failures
**Status**: ROOT CAUSE FOUND
- `/products` endpoint returning 500 errors
- Search, sort, filter all failing
- Database query: `column "search" does not exist`

**Investigation Progress**:
- [x] Check ToDBFilters() method in filter_state_query.go
- [x] Check database repository layer and query builder

**FINDINGS**:
- **ROOT CAUSE**: Line 91 in `filter_state_query.go`: `result["search"] = fs.SearchTerm`
- **ISSUE**: Maps search term to "search" column but database expects `title_ilike` pattern
- **CONFIRMED**: Query builder doesn't handle "search" key - it tries to create: `WHERE search = 'gehakt'`
- **SOLUTION NEEDED**: Change line 91 to: `result["title_ilike"] = "%" + fs.SearchTerm + "%"`

**Complete Summary**:
1. **Template**: Missing `.filter-button` in header, wrong selector `.close-button` vs `.filter-sidebar__close-button`
2. **JavaScript**: Theme toggle missing handler, sidebar toggle broken due to missing elements
3. **Backend**: Search filter incorrectly mapped to non-existent "search" column
4. **Result**: Cascading failure - no UI controls work, all API calls fail with 500 errors

### 4. Auth Integration Issues
**Status**: CRITICAL ISSUE FOUND
- New auth system (Clerk → Native Go) impact
- CSRF token handling changes
- Session management affecting requests

**Investigation Progress**:
- [x] Check CSRF token processing in filter forms
- [ ] Verify session management in new auth system
- [ ] Check if auth middleware affects /products endpoint

**FINDINGS**:
- **CRITICAL**: CSRF tokens incorrectly processed as filter parameters
- **LOG EVIDENCE**: `"[DEBUG] getFilterStateFromRequest hit DEFAULT case for potential filter" key=csrf_token`
- **IMPACT**: CSRF tokens added to filter map, then passed to database as WHERE conditions
- **RESULT**: Contributes to malformed database queries beyond just the "search" column issue

### 5. CSS/Styling Issues
**Status**: Pending
- Products possibly loading but invisible
- Theme toggle functionality broken
- Filter sidebar visibility issues

## Investigation Plan
1. **Templates** - DOM elements missing (CURRENT)
2. **JavaScript** - Module loading/execution errors  
3. **Backend** - /products endpoint 500 errors
4. **Auth integration** - CSRF/session impacts
5. **CSS** - Visibility/styling issues

## DEEPER ROOT CAUSE ANALYSIS

### Core Pattern: Form Processing vs UI Component Mismatch

**The Real Issue**: Extensive refactoring broke the connection between frontend UI components and their corresponding backend processing logic.

### Cascading Failure Chain:
1. **Templates missing UI elements** → JavaScript can't bind to controls
2. **Form processing doesn't filter system data** → CSRF tokens treated as filters  
3. **Database mapping changed** → "search" column doesn't exist
4. **CSS selectors mismatched** → Styling not applied correctly

### Systemic Issues:
- **Frontend-Backend Contract Broken**: Form field names, UI selectors, and server processing out of sync
- **Data Validation Missing**: No filtering of system vs user data in request processing
- **Component Integration Lost**: UI controls, JavaScript handlers, and CSS selectors misaligned

### Complete Fix Requirements:
1. **Backend**: Fix search mapping (`result["search"]` → `result["title_ilike"]`)
2. **Backend**: Filter out CSRF tokens from filter processing  
3. **Templates**: Add missing filter button, fix CSS selectors
4. **JavaScript**: Fix DOM selectors, add theme toggle handler
5. **CSS**: Align class names with JavaScript expectations

## PROPOSED SOLUTION

### Fix Implementation Order (Critical Path)

#### 1. Backend Database Issues (CRITICAL - Blocks All API Calls)
**File**: `/internal/models/filter_state_query.go`
- **Line 91**: Change `result["search"] = fs.SearchTerm` to `result["title_ilike"] = "%" + fs.SearchTerm + "%"`
- **Add CSRF filtering**: Exclude `csrf_token` from filter processing in `processGeneralFilters()`

#### 2. Missing UI Elements (HIGH - Breaks User Interface)
**File**: `/frontend/templates/partials/header.html`
- **Add filter button**: Insert filter toggle button before theme toggle
- **Example**: `<button class="filter-button" aria-label="Open filters">Filter</button>`

#### 3. JavaScript Selector Fixes (HIGH - Enables UI Functionality)  
**File**: `/frontend/js/modules/sidebar_toggle.js`
- **Line 78**: Change `.close-button` to `.filter-sidebar__close-button`
- **Add theme toggle handler** in event_handlers.js for `data-action="toggle-theme"`

#### 4. CSS Class Alignment (MEDIUM - Visual Consistency)
**Files**: CSS or JavaScript alignment needed
- **Option A**: Change CSS `.is-visible` to `.active` 
- **Option B**: Change JavaScript to use `.is-visible`

### Expected Results After Fixes:
- ✅ `/products` API calls return 200 instead of 500
- ✅ Filter sidebar opens/closes correctly
- ✅ Search, sort, filter functionality restored
- ✅ Theme toggle works
- ✅ Products display properly in viewport

### Testing Validation:
1. Search for "gehakt" → Should return products, not 500 error
2. Click filter button → Sidebar should open
3. Sort by price → Should update product list  
4. Toggle theme → Should change light/dark mode

## EXECUTIVE SUMMARY

### Investigation Scope
Comprehensive analysis of cascading system failures following extensive refactoring of:
- Authentication system (Clerk → Native Go + HTMX)
- File structure reorganization (large file splitting)
- JavaScript module restructuring  
- UI component integration

### Critical Findings Summary
| Issue Category | Status | Impact | Root Cause |
|---|---|---|---|
| Template/DOM | ❌ Critical | UI non-functional | Missing filter button, selector mismatches |
| JavaScript | ❌ Major | No user interaction | Broken DOM bindings, missing handlers |
| Backend API | ❌ Critical | All endpoints 500 | Malformed SQL queries |
| Auth Integration | ❌ Major | Data pollution | CSRF tokens treated as filters |
| CSS/Styling | ⚠️ Minor | Visual inconsistency | Class name misalignment |

### Systemic Root Cause
**Post-refactoring integration failure**: Individual components work in isolation but their interconnections are broken. The extensive refactoring broke the implicit contracts between:
- Frontend form field names ↔ Backend parameter processing
- JavaScript selectors ↔ Template element classes  
- UI component interactions ↔ Event handling
- System data ↔ User data separation

### Business Impact
- **Complete loss of core functionality**: Search, filter, sort operations
- **Broken user experience**: No interactive controls work
- **API failure cascade**: All product-related endpoints returning 500 errors
- **Development productivity**: Asset system works, but application unusable

### Recovery Strategy
**Phase 1 (Critical - 30 minutes)**:
- Fix database query mapping (1 line change)
- Add CSRF token filtering (prevent data pollution)

**Phase 2 (High Priority - 1 hour)**:
- Add missing filter button to header template
- Fix JavaScript DOM selectors

**Phase 3 (Medium Priority - 30 minutes)**:
- Align CSS classes with JavaScript expectations
- Add theme toggle handler

**Total Estimated Recovery Time**: 2 hours

### Success Criteria
✅ All `/products` API endpoints return 200 status codes  
✅ Search functionality returns relevant results  
✅ Filter sidebar opens and closes correctly  
✅ Sort and filter operations update product display  
✅ Theme toggle switches between light/dark modes  
✅ No JavaScript console errors  

### Lessons Learned
1. **Integration testing critical** during extensive refactoring
2. **Frontend-backend contracts** need explicit documentation
3. **Component interdependencies** should be mapped before changes
4. **System vs user data** separation must be maintained in request processing

### Recommendation
Implement systematic integration testing to prevent similar cascading failures in future refactoring efforts. Consider contract-first development for frontend-backend communication.

---
**Report Generated**: May 22, 2025  
**Investigation Duration**: 3 hours  
**Status**: Complete - Ready for Implementation