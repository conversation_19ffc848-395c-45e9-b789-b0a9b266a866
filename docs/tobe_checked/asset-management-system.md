# Omfietser Asset Management System Architecture

## Overview

The asset management system handles resolving and serving static assets (JS, CSS, images) across different environments (development/production) with support for CDN integration. It manages the complexity of asset versioning through Vite's manifest.

## Core Components

### 1. Asset Core (`asset_core.go`)

Responsible for:
- Loading and parsing the Vite manifest
- Environment detection (development/production)
- Global configuration management
- One-time initialization of the asset system

Key functions:
- `InitAssets(cfg *config.Config)`: Sets up the asset management system based on config
- `loadManifest(manifestPath string)`: Parses the Vite manifest.json file

### 2. Asset Paths (`asset_paths.go`)

Responsible for:
- Resolving file paths consistently across environments
- File type detection and special handling
- CDN URL generation when enabled

Key functions:
- `AssetPath(assetPath string)`: Main entry point for resolving asset paths
- `resolveJSCSSPath(cleanPath, isJS, isCSS, log)`: Special handling for JS/CSS files
- `resolveCSSThroughJS(cleanPath, log)`: Finds CSS files associated with JS entries
- `resolveFinalURL(localStaticPath, log)`: Applies CDN prefix if enabled

### 3. Asset Scripts (`asset_scripts.go`)

Responsible for:
- Providing asset paths for template injection
- Managing script paths with backward compatibility
- Special handling for PWA files

Key functions:
- `GetScriptPaths(cfg *config.Config)`: Generates a map of script paths for templates
- `GetMainJSPath/GetEntryJSPath/GetAppJSPath`: Helper functions for finding specific entry points
- `ServePWAFile`: Special handling for PWA manifest and service worker files

## Environment-Specific Behavior

### Development Mode

1. **JavaScript Files**:
   - Entry points (app.js) are served from Vite dev server
   - Libraries (htmx.min.js) are served from static directory
   - The Vite client is injected for HMR support

2. **CSS Files**:
   - CSS is loaded through JavaScript by Vite
   - No separate CSS URL is needed in templates
   - `MainCSSPath` is intentionally empty in dev mode

3. **Images and Other Assets**:
   - Served directly from static directory
   - No versioning/hashing applied

### Production Mode

1. **JavaScript Files**:
   - Paths resolved through the manifest to get hashed filenames
   - Files served from static directory or CDN
   - Versioning through filename hashes for cache control

2. **CSS Files**:
   - Either directly through manifest or via JS entry's CSS array
   - Separate CSS files with version hashes
   - Explicit link tags in templates

3. **Images and Other Assets**:
   - Served from static directory or CDN
   - May have versioned filenames via the manifest

## Asset Flow Process

1. Template requests an asset via `{{ AssetPath "path/to/asset.ext" }}`
2. `AssetPath` determines the asset type and environment
3. Type-specific resolution logic is applied:
   - JS/CSS: Special handling based on environment and manifest
   - Images: Direct static path or CDN URL
4. Final URL is returned to the template for rendering

## Manifest Integration

The system uses Vite's manifest.json for asset versioning:

```json
{
  "js/app.js": {
    "file": "assets/app-9e_HAdUy.js",
    "name": "app",
    "src": "js/app.js",
    "isEntry": true,
    "css": [
      "assets/css/app-BYyIMUtV.css"
    ]
  }
}
```

This enables:
- Consistent asset references regardless of filename changes
- Automatic CSS extraction and association with JS entries
- Cache-friendly versioned assets in production

## Template Integration

Asset paths are injected into templates via the `AddBaseTemplateData` function:

```go
data["MainJSPath"] = scriptPaths["MainJSPath"]   // Main JS path
data["HTMXPath"] = scriptPaths["HTMXPath"]       // HTMX path
data["MainCSSPath"] = scriptPaths["MainCSSPath"] // Main CSS path
```

Then used in templates with environment-specific logic:

```html
<!-- HTMX -->
<script src="{{ .HTMXPath }}" defer></script>

<!-- Main Application JS & Vite Client -->
{{ if .ViteDevMode }}
  <!-- Development mode with Vite -->
  <script type="module" src="{{ .ViteClientSrc }}"></script>
  <script type="module" src="{{ .MainJSPath }}"></script>
{{ else }}
  <!-- Production mode -->
  <script type="module" src="{{ .MainJSPath }}" defer></script>
{{ end }}
```

## CDN Integration

When CDN is enabled:
1. Assets are still generated locally during build
2. Path resolution prepends CDN base URL to asset paths
3. Templates render URLs pointing to CDN
4. Local static directory serves as fallback

## Common Issues and Solutions

1. **CSS Not Loading in Development**:
   - In development, CSS is loaded through JS by Vite
   - No separate CSS link tags should be used
   - `MainCSSPath` is intentionally empty in development mode

2. **JS Path Resolution**:
   - Entry points need special handling in development vs production
   - Libraries need consistent fallback paths
   - Manifest lookup may fail for non-Vite-processed files

3. **Path Consistency**:
   - Manifest keys must match the requested paths exactly
   - Path cleaning and normalization is essential
   - File extension detection determines resolution logic