# Circular Dependency Fix Plan

This document outlines a detailed, step-by-step plan to resolve the circular dependency issues in the Omfietser codebase. The plan focuses on minimal changes to break the dependency cycles while completing the authentication implementation.

## Overview

The main circular dependency chain is:
```
cmd/debug_assets -> internal/handlers -> internal/auth -> internal/service -> internal/service/comparison -> internal/service -> ...
```

We'll address this with targeted changes to break the most critical cycles without major restructuring.

## Phase 1: Create Interfaces Package

### Step 1: Create User Service Interface Package
1. Create a new package `internal/interfaces`
2. Create a file `internal/interfaces/user_service.go`
3. Move the `UserServiceInterface` from `internal/service/user_service.go` to this new file
4. Update imports in all files that use this interface

```go
// internal/interfaces/user_service.go
package interfaces

import (
    "context"
    "github.com/coolbox13/server_go/internal/models"
)

// UserServiceInterface defines the operations provided by the user service.
type UserServiceInterface interface {
    // GetUserByID retrieves a user by their ID.
    GetUserByID(ctx context.Context, userID int64) (models.User, error)

    // AuthenticateUser authenticates a user with email/username and password.
    AuthenticateUser(ctx context.Context, emailOrUsername, password string) (models.User, error)

    // RegisterUser registers a new user.
    RegisterUser(ctx context.Context, email, username, password string) (models.User, error)

    // UpdateLastLogin updates the last login time for a user.
    UpdateLastLogin(ctx context.Context, userID int64) error

    // Other methods as needed...
}
```

### Step 2: Update Service Implementation
1. Update `internal/service/user_service.go` to implement the interface from the new package
2. Fix imports and ensure the implementation satisfies the interface

```go
// internal/service/user_service.go
package service

import (
    "context"
    "github.com/coolbox13/server_go/internal/interfaces"
    "github.com/coolbox13/server_go/internal/models"
    // Other imports...
)

// Ensure UserService implements UserServiceInterface
var _ interfaces.UserServiceInterface = (*UserService)(nil)

// UserService implements user-related business logic.
type UserService struct {
    // Fields...
}

// Implementation methods...
```

### Step 3: Update Auth Package
1. Update `internal/auth/jwt.go` to use the interface from the new package
2. Fix imports and ensure the code works with the interface

```go
// internal/auth/jwt.go
package auth

import (
    "github.com/coolbox13/server_go/internal/interfaces"
    // Other imports...
)

// JWTAuth handles JWT authentication operations.
type JWTAuth struct {
    userService interfaces.UserServiceInterface
    // Other fields...
}

// Update constructor and methods to use the interface
```

### Step 4: Update Service Factory
1. Update `internal/service/factory/factory.go` to return the interface type
2. Fix the return type in the `User()` method

```go
// internal/service/factory/factory.go
package factory

import (
    "github.com/coolbox13/server_go/internal/interfaces"
    // Other imports...
)

// Factory creates and manages services.
type Factory struct {
    // Other fields...
    userService interfaces.UserServiceInterface
}

// User returns the user service instance.
func (f *Factory) User() interfaces.UserServiceInterface {
    if f.userService == nil {
        f.userService = service.NewUserService(
            f.config,
            f.cache,
            f.repoFactory.User(),
        )
    }
    return f.userService
}
```

## Phase 2: Fix Comparison Service Circular Dependency

### Step 1: Update Comparison Service Base
1. Modify `internal/service/comparison/service.go` to use `servicecore.BaseService` instead of `service.BaseService`
2. Update imports and fix any related code

```go
// internal/service/comparison/service.go
package comparison

import (
    "github.com/coolbox13/server_go/internal/servicecore"
    // Other imports, but NOT internal/service
)

// Service handles price comparison logic.
type Service struct {
    *servicecore.BaseService
    // Other fields...
}

// Update constructor to use servicecore.NewBaseService
func New(...) *Service {
    return &Service{
        BaseService: servicecore.NewBaseService(cfg, cache),
        // Other fields...
    }
}
```

### Step 2: Update Comparison Adapter
1. Ensure `internal/service/comparison_adapter.go` properly adapts the comparison service
2. Fix any imports or method calls that might be affected by the base service change

```go
// internal/service/comparison_adapter.go
package service

import (
    "github.com/coolbox13/server_go/internal/service/comparison"
    // Other imports...
)

// ComparisonService is maintained for backwards compatibility.
type ComparisonService struct {
    *comparison.Service
}

// NewComparisonService creates a new comparison service.
func NewComparisonService(...) *ComparisonService {
    // Create the new comparison service
    service := comparison.New(cfg, cache, productRepo, equivalenceRepo, priceHistoryRepo)

    // Wrap it in the backwards-compatible adapter
    return &ComparisonService{
        Service: service,
    }
}
```

## Phase 3: Complete Auth Implementation

### Step 1: Ensure Session Manager Works Correctly
1. Verify `internal/auth/session_manager.go` is properly implemented
2. Fix any issues with Redis integration or session handling

### Step 2: Update Auth Handler
1. Ensure `internal/handlers/auth_handler.go` uses the session manager correctly
2. Implement any missing functionality for login, signup, and logout

### Step 3: Update Auth Middleware
1. Verify `internal/router/middleware.go` auth middleware functions work correctly
2. Ensure they use the updated interfaces and session manager

### Step 4: Update Frontend Templates
1. Update login and signup templates to work with the new auth system
2. Ensure CSRF tokens are properly handled in forms

## Phase 4: Testing and Verification

### Step 1: Run Linter
1. Run `go vet ./...` to check for remaining circular dependencies
2. Fix any additional issues found

### Step 2: Run Tests
1. Run existing tests to ensure functionality still works
2. Add new tests for auth functionality if needed

### Step 3: Manual Testing
1. Test login, signup, and logout flows
2. Test authenticated routes and session management
3. Verify CSRF protection works correctly

## Phase 5: Cleanup

### Step 1: Remove Unused Code
1. Remove any remaining code related to Clerk auth
2. Clean up any temporary code added during the transition

### Step 2: Update Documentation
1. Update comments and documentation to reflect the new auth system
2. Document the interfaces package and its purpose

## Implementation Notes

- Make changes incrementally, committing after each logical step
- Run tests frequently to catch issues early
- Focus on breaking the circular dependencies first, then complete the auth implementation
- Avoid making unnecessary changes to minimize risk

## Fallback Plan

If issues arise during implementation:
1. Revert to the last known good state
2. Consider a more targeted approach focusing only on the most critical circular dependency
3. If necessary, temporarily disable linting for specific files while completing the auth implementation

This plan provides a structured approach to resolving the circular dependencies while completing the authentication implementation with minimal changes to the codebase.

## Implementation Status

### Completed Tasks

- [x] Created interfaces package with UserServiceInterface
- [x] Updated auth/jwt.go to use the interface instead of concrete implementation
- [x] Moved user service implementation to dedicated user package
- [x] Created adapter in service package for backward compatibility
- [x] Updated service factory to use the interface

### Remaining Tasks

- [x] Fix comparison service circular dependency
- [x] Complete auth implementation
- [x] Run tests and verify functionality
- [x] Clean up unused code
- [x] Update documentation

### Progress Summary

The circular dependency between the `auth` and `service` packages has been successfully resolved by:

1. Creating an `interfaces` package with `UserServiceInterface` that defines the contract needed by the auth package
2. Moving the user service implementation to a dedicated `service/user` package
3. Creating an adapter in the `service` package that implements the interface and delegates to the new implementation
4. Updating `auth/jwt.go` to depend on the interface instead of the concrete implementation
5. Updating the service factory to return the interface type

The circular dependency with the comparison service has also been resolved:

1. The comparison service now uses `servicecore.BaseService` instead of `service.BaseService`
2. A proper adapter in `comparison_adapter.go` forwards to the new implementation
3. The `service/base_service.go` file provides an alias to `servicecore.BaseService`

This approach follows the Dependency Inversion Principle, where both high-level modules (auth, comparison) and low-level modules (service) depend on abstractions (interfaces, core services) rather than concrete implementations.

The changes have been committed and the application has been tested to ensure functionality is preserved. The build now succeeds without any circular dependency errors.

### Completion Status

✅ **All tasks completed successfully!**

The circular dependency issues have been fully resolved, and the authentication system has been successfully migrated from Clerk to a native Go implementation. The codebase now has:

1. A clean architecture with proper separation of concerns
2. No circular dependencies between packages
3. A fully functional native authentication system
4. Comprehensive test coverage for the auth components
5. Updated documentation reflecting the changes

The application has been tested and is working correctly with the new authentication system.
