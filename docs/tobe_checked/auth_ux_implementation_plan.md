# Authentication UX Implementation Plan

**Date:** January 6, 2025  
**Based on:** Authentication audit findings from `/docs/auth_audit_010625.md`

## Executive Summary

This plan addresses critical authentication UX issues identified in the audit and implements significant UI/UX improvements. The primary focus is on fixing session persistence, removing problematic HX-Redirect usage, and implementing full-page authentication navigation instead of dropdown menus.

## Current Issues to Fix

### 🔴 Critical Issues (from audit)
1. **HX-Redirect causing full page reloads** → Breaking session persistence
2. **Session context loss** → Users lose login status on app refresh
3. **UpdateLastLogin corrupting password_hash** → Database integrity issues
4. **Inconsistent HTMX event handling** → Authentication state confusion

### 🟡 UX Issues (new requirements)
1. **Dropdown menu complexity** → Replace with full-page navigation
2. **App logo refresh losing auth** → Preserve authentication across refreshes
3. **Poor user management interface** → Need dedicated user management page

## Implementation Plan

### Phase 1: Critical Authentication Fixes (Priority 1)

#### 1.1 Remove HX-Redirect Usage
**Files:** `internal/handlers/auth_handler.go`

**Current problematic code:**
```go
if r.<PERSON>er.Get("HX-Request") == "true" {
    w.Header().Set("HX-Redirect", "/")
    w.WriteHeader(http.StatusOK)
    return
}
```

**New implementation:**
```go
if r.Header.Get("HX-Request") == "true" {
    // Return success content and let frontend handle navigation
    w.Header().Set("HX-Trigger", "auth-success")
    w.Header().Set("Content-Type", "text/html")
    w.WriteHeader(http.StatusOK)
    w.Write([]byte(`<div class="auth-success-message">Login successful</div>`))
    return
}
```

#### 1.2 Fix HTMX Event Handling
**Files:** `frontend/js/modules/htmx-setup.js`

**Remove problematic beforeOnLoad handler:**
```javascript
// REMOVE THIS:
document.body.addEventListener('htmx:beforeOnLoad', (event) => {
    const hxRedirect = event.detail.xhr.getResponseHeader('HX-Redirect');
    // ... problematic redirect handling
});
```

**Add proper auth-success handling:**
```javascript
// Listen for auth-success trigger
document.body.addEventListener('htmx:trigger', (event) => {
    if (event.detail.trigger === 'auth-success') {
        updateUserButtonState(true);
        // Navigate to product page content
        htmx.ajax('GET', '/products', {target: 'main', swap: 'innerHTML'});
    }
});
```

#### 1.3 Fix UpdateLastLogin Database Corruption
**Files:** `internal/service/user/service.go`

**Current problematic fallback:**
```go
// Fallback to the old method if the repository doesn't support the new method
user, err := s.GetUserByID(ctx, userID)
// ... updates entire user, potentially corrupting password_hash
```

**New implementation:**
```go
// Use only the dedicated UpdateLastLogin method
func (s *Service) UpdateLastLogin(ctx context.Context, userID int64) error {
    lastLogin := time.Now()
    return s.UserRepo.UpdateLastLogin(ctx, userID, lastLogin)
}
```

#### 1.4 Fix Database Query Mismatches
**Files:** `internal/repository/postgres/user/queries.go`

**Current problematic query:**
```sql
SELECT id, email, username, password_hash, role, is_active, is_subscribed,
```

**Fixed query:**
```sql
SELECT id, email, first_name, last_name, password_hash, role, is_active, is_subscribed,
       subscription_status, trial_start_date, trial_end_date, subscription_start, subscription_end, last_login, created_at, updated_at
```

### Phase 2: Session Persistence Implementation (Priority 1)

#### 2.1 Enhance Session Cookie Configuration
**Files:** `internal/auth/session_manager.go`

**Current cookie settings:**
```go
cookie := &http.Cookie{
    Name:     SessionCookieName,
    Value:    sessionID,
    Path:     "/",
    Expires:  expiresAt,
    HttpOnly: true,
    Secure:   !sm.cfg.IsDevelopment(),
    SameSite: http.SameSiteLaxMode,
}
```

**Enhanced cookie settings:**
```go
cookie := &http.Cookie{
    Name:     SessionCookieName,
    Value:    sessionID,
    Path:     "/",
    Expires:  expiresAt,
    MaxAge:   int(sessionDuration.Seconds()), // Add MaxAge for better compatibility
    HttpOnly: true,
    Secure:   !sm.cfg.IsDevelopment(),
    SameSite: http.SameSiteLaxMode,
}
```

#### 2.2 Add Session Validation Middleware Enhancement
**Files:** `internal/middleware/middleware.go`

**Add session refresh logic:**
```go
func (mw *Middleware) AuthMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        cookie, err := r.Cookie(auth.SessionCookieName)
        if err != nil {
            next.ServeHTTP(w, r)
            return
        }

        userID, csrfToken, err := mw.SessionManager.GetSessionData(r.Context(), cookie.Value)
        if err != nil {
            // Session expired or invalid, clear the cookie
            mw.SessionManager.ClearSessionCookie(w)
            next.ServeHTTP(w, r)
            return
        }

        // Refresh session if it's close to expiry (implement session sliding)
        if mw.shouldRefreshSession(cookie.Value) {
            newSessionID, newCSRFToken, newExpiresAt, err := mw.SessionManager.RefreshSession(r.Context(), cookie.Value, userID)
            if err == nil {
                mw.SessionManager.SetSessionCookie(w, newSessionID, newExpiresAt)
                csrfToken = newCSRFToken
            }
        }

        // Continue with user context
        user, err := mw.UserService.GetUserByID(r.Context(), userID)
        if err != nil {
            mw.SessionManager.ClearSessionCookie(w)
            next.ServeHTTP(w, r)
            return
        }

        ctx := context.WithValue(r.Context(), auth.UserContextKey, user)
        ctx = context.WithValue(ctx, auth.CSRFTokenContextKey, csrfToken)
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}
```

#### 2.3 Add Session Refresh Method
**Files:** `internal/auth/session_manager.go`

```go
// RefreshSession extends an existing session
func (sm *SessionManager) RefreshSession(ctx context.Context, oldSessionID string, userID int64) (string, string, time.Time, error) {
    // Delete old session
    err := sm.DeleteSession(ctx, oldSessionID)
    if err != nil {
        logging.L().Warn("Failed to delete old session during refresh", "sessionID", oldSessionID, "error", err)
    }

    // Create new session
    return sm.CreateSession(ctx, userID)
}

// shouldRefreshSession checks if a session should be refreshed
func (sm *SessionManager) shouldRefreshSession(sessionID string) bool {
    // Implement logic to check if session is close to expiry
    // For example, refresh if less than 1 hour remaining
    sessionData, err := sm.getSessionFromRedis(context.Background(), sessionID)
    if err != nil {
        return false
    }

    timeToExpiry := time.Until(sessionData.ExpiresAt)
    return timeToExpiry < time.Hour
}
```

### Phase 3: Full-Page Authentication Navigation (Priority 2)

#### 3.1 Remove Dropdown Menu Functionality
**Files:** `frontend/templates/partials/header.html`

**Current dropdown implementation:** (lines 47-107)
```html
<div class="header__user-menu">
    <button class="header__icon-button header__user-button header__user-button--logged-out"
            onclick="toggleUserMenu()"
            aria-label="User Authentication">
        <!-- dropdown content -->
    </button>
</div>
```

**New full-page navigation:**
```html
{{ if .IsLoggedIn }}
    <!-- Logged-in user: Navigate to user management page -->
    <button class="header__icon-button header__user-button header__user-button--logged-in"
            hx-get="/user/manage"
            hx-target="main"
            hx-swap="innerHTML"
            hx-push-url="true"
            aria-label="User Management">
        <img src="{{ AssetPath "images/user-icon.svg" }}" alt="User Management" hx-preserve="true">
    </button>
{{ else }}
    <!-- Not logged in: Navigate to authentication page -->
    <button class="header__icon-button header__user-button header__user-button--logged-out"
            hx-get="/auth"
            hx-target="main"
            hx-swap="innerHTML"
            hx-push-url="true"
            aria-label="Login or Sign Up">
        <img src="{{ AssetPath "images/user-icon.svg" }}" alt="Login or Sign Up" hx-preserve="true">
    </button>
{{ end }}
```

#### 3.2 Create Authentication Page Template
**Files:** `frontend/templates/pages/auth.html`

```html
{{ define "auth-page" }}
<div class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1 class="auth-title">Welcome to Omfietser</h1>
            <p class="auth-subtitle">Sign in to your account or create a new one</p>
        </div>

        <div class="auth-tabs">
            <button class="auth-tab auth-tab--active" data-tab="login">Sign In</button>
            <button class="auth-tab" data-tab="signup">Sign Up</button>
        </div>

        <div class="auth-content">
            <div id="login-tab" class="auth-tab-content auth-tab-content--active">
                {{ template "login-form" . }}
            </div>
            <div id="signup-tab" class="auth-tab-content">
                {{ template "signup-form" . }}
            </div>
        </div>

        <div class="auth-footer">
            <button hx-get="/" 
                    hx-target="main" 
                    hx-swap="innerHTML" 
                    hx-push-url="true"
                    class="auth-back-button">
                ← Back to Home
            </button>
        </div>
    </div>
</div>
{{ end }}
```

#### 3.3 Create User Management Page Template
**Files:** `frontend/templates/pages/user-manage.html`

```html
{{ define "user-manage-page" }}
<div class="user-manage-page">
    <div class="user-manage-container">
        <div class="user-manage-header">
            <h1 class="user-manage-title">User Management</h1>
            <p class="user-manage-subtitle">Welcome back, {{ .User.FirstName }}!</p>
        </div>

        <div class="user-manage-sections">
            <!-- User Profile Section -->
            <section class="user-section">
                <h2 class="user-section-title">Profile Information</h2>
                <div class="user-profile-info">
                    <div class="user-info-item">
                        <label>Email:</label>
                        <span>{{ .User.Email }}</span>
                    </div>
                    <div class="user-info-item">
                        <label>Name:</label>
                        <span>{{ .User.FirstName }} {{ .User.LastName }}</span>
                    </div>
                    <div class="user-info-item">
                        <label>Member since:</label>
                        <span>{{ .User.CreatedAt.Format "January 2, 2006" }}</span>
                    </div>
                    <div class="user-info-item">
                        <label>Last login:</label>
                        <span>{{ if .User.LastLogin }}{{ .User.LastLogin.Format "January 2, 2006 at 3:04 PM" }}{{ else }}Never{{ end }}</span>
                    </div>
                </div>
            </section>

            <!-- Preferences Section -->
            <section class="user-section">
                <h2 class="user-section-title">Preferences</h2>
                <div class="user-preferences">
                    <p class="placeholder-text">User Preferences - Coming Soon</p>
                    <p class="placeholder-description">
                        We're working on adding customization options for your shopping experience.
                    </p>
                </div>
            </section>

            <!-- Account Actions Section -->
            <section class="user-section">
                <h2 class="user-section-title">Account Actions</h2>
                <div class="user-actions">
                    <form hx-post="/logout" 
                          hx-target="main" 
                          hx-swap="innerHTML"
                          hx-push-url="true"
                          class="logout-form">
                        <input type="hidden" name="csrf_token" value="{{ .CSRFToken }}">
                        <button type="submit" class="button button--danger">
                            Sign Out
                        </button>
                    </form>
                </div>
            </section>
        </div>

        <div class="user-manage-footer">
            <button hx-get="/products" 
                    hx-target="main" 
                    hx-swap="innerHTML" 
                    hx-push-url="true"
                    class="button button--secondary">
                ← Back to Products
            </button>
        </div>
    </div>
</div>
{{ end }}
```

#### 3.4 Add New Route Handlers
**Files:** `internal/handlers/auth_handler.go`

```go
// HandleAuthPage renders the full-page authentication interface
func (ah *AuthHandler) HandleAuthPage(w http.ResponseWriter, r *http.Request) {
    // Check if user is already logged in
    if user, ok := r.Context().Value(auth.UserContextKey).(*models.User); ok && user != nil {
        // Redirect logged-in users to user management
        if r.Header.Get("HX-Request") == "true" {
            w.Header().Set("HX-Redirect", "/user/manage")
            w.WriteHeader(http.StatusOK)
            return
        }
        http.Redirect(w, r, "/user/manage", http.StatusSeeOther)
        return
    }

    data := ah.NewBasePageData(r, "Authentication - Omfietser", models.User{}, nil)
    data["ContentTemplateName"] = "auth-page"
    
    if r.Header.Get("HX-Request") == "true" {
        // Return only the main content for HTMX requests
        ah.RenderContentTemplate(w, r, "auth-page", data)
    } else {
        // Return full page for direct navigation
        ah.RenderPage(w, r, "base", data)
    }
}

// HandleUserManagePage renders the user management page
func (ah *AuthHandler) HandleUserManagePage(w http.ResponseWriter, r *http.Request) {
    // Ensure user is logged in
    user, ok := r.Context().Value(auth.UserContextKey).(*models.User)
    if !ok || user == nil {
        if r.Header.Get("HX-Request") == "true" {
            w.Header().Set("HX-Redirect", "/auth")
            w.WriteHeader(http.StatusOK)
            return
        }
        http.Redirect(w, r, "/auth", http.StatusSeeOther)
        return
    }

    data := ah.NewBasePageData(r, "User Management - Omfietser", *user, nil)
    data["ContentTemplateName"] = "user-manage-page"
    
    if r.Header.Get("HX-Request") == "true" {
        // Return only the main content for HTMX requests
        ah.RenderContentTemplate(w, r, "user-manage-page", data)
    } else {
        // Return full page for direct navigation
        ah.RenderPage(w, r, "base", data)
    }
}
```

#### 3.5 Update Route Registration
**Files:** `internal/handlers/auth_handler.go`

```go
// RegisterRoutes sets up the routes for the AuthHandler.
func (ah *AuthHandler) RegisterRoutes(router *mux.Router) {
    // Authentication pages
    router.HandleFunc("/auth", ah.HandleAuthPage).Methods("GET")
    router.HandleFunc("/user/manage", ah.HandleUserManagePage).Methods("GET")
    
    // Authentication actions
    router.HandleFunc("/login", ah.HandleLogin).Methods("POST")
    router.HandleFunc("/signup", ah.HandleSignup).Methods("POST")
    router.HandleFunc("/logout", ah.HandleLogout).Methods("POST", "GET")
    
    // Legacy route redirects (for backward compatibility)
    router.HandleFunc("/login", func(w http.ResponseWriter, r *http.Request) {
        http.Redirect(w, r, "/auth", http.StatusSeeOther)
    }).Methods("GET")
    router.HandleFunc("/signup", func(w http.ResponseWriter, r *http.Request) {
        http.Redirect(w, r, "/auth", http.StatusSeeOther)
    }).Methods("GET")
}
```

### Phase 4: Fix App Logo Refresh Issue (Priority 2)

#### 4.1 Update Header Logo Behavior
**Files:** `frontend/templates/partials/header.html`

**Current problematic logo:**
```html
<a href="/" class="header__logo">
    <img src="{{ AssetPath "images/bike.svg" }}" alt="Omfietser" class="header__logo-icon" hx-preserve="true">
</a>
```

**New session-preserving logo:**
```html
<button class="header__logo"
        hx-get="/"
        hx-target="main"
        hx-swap="innerHTML"
        hx-push-url="true"
        aria-label="Home">
    <img src="{{ AssetPath "images/bike.svg" }}" alt="Omfietser" class="header__logo-icon" hx-preserve="true">
</button>
```

#### 4.2 Add CSS for Logo Button
**Files:** `frontend/scss/layouts/_header.scss`

```scss
.header__logo {
    // Reset button styles
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    
    // Maintain existing logo styles
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--color-primary);
    
    &:hover {
        opacity: 0.8;
    }
    
    &:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
        border-radius: 4px;
    }
}
```

### Phase 5: Enhanced JavaScript Auth State Management (Priority 2)

#### 5.1 Create Authentication State Manager
**Files:** `frontend/js/modules/auth-state.js`

```javascript
/**
 * Authentication State Manager
 * 
 * Manages authentication state across the application
 * - Tracks login status
 * - Handles state persistence
 * - Provides state change notifications
 */

// Module state
const state = {
    isLoggedIn: false,
    user: null,
    initialized: false,
    listeners: []
};

/**
 * Initialize authentication state management
 */
export function init() {
    if (state.initialized) {
        console.log('Auth state already initialized');
        return;
    }

    console.log('Initializing authentication state manager');

    // Check initial authentication state from DOM
    checkInitialAuthState();

    // Set up HTMX event listeners for auth state changes
    setupEventListeners();

    state.initialized = true;
}

/**
 * Check initial authentication state from DOM
 */
function checkInitialAuthState() {
    // Check if user button indicates logged-in state
    const userButton = document.querySelector('.header__user-button');
    if (userButton) {
        const isLoggedIn = userButton.classList.contains('header__user-button--logged-in');
        updateAuthState(isLoggedIn);
    }

    // Try to extract user data from meta tags or data attributes
    const userDataMeta = document.querySelector('meta[name="user-data"]');
    if (userDataMeta) {
        try {
            const userData = JSON.parse(userDataMeta.content);
            state.user = userData;
        } catch (e) {
            console.warn('Failed to parse user data from meta tag:', e);
        }
    }
}

/**
 * Set up event listeners for authentication state changes
 */
function setupEventListeners() {
    // Listen for successful authentication
    document.body.addEventListener('htmx:trigger', (event) => {
        if (event.detail.trigger === 'auth-success') {
            updateAuthState(true);
        }
    });

    // Listen for logout
    document.body.addEventListener('htmx:afterRequest', (event) => {
        if (event.detail.requestConfig.path === '/logout' && event.detail.successful) {
            updateAuthState(false);
        }
    });

    // Listen for authentication page loads
    document.body.addEventListener('htmx:afterSwap', (event) => {
        // Re-check auth state after any page swap
        setTimeout(checkInitialAuthState, 100);
    });
}

/**
 * Update authentication state
 * @param {boolean} isLoggedIn - New login status
 * @param {Object} [user] - User data (optional)
 */
export function updateAuthState(isLoggedIn, user = null) {
    const wasLoggedIn = state.isLoggedIn;
    
    state.isLoggedIn = isLoggedIn;
    if (user) {
        state.user = user;
    } else if (!isLoggedIn) {
        state.user = null;
    }

    // Notify listeners if state changed
    if (wasLoggedIn !== isLoggedIn) {
        notifyListeners(isLoggedIn);
    }

    console.log(`Auth state updated: ${isLoggedIn ? 'logged in' : 'logged out'}`);
}

/**
 * Add a listener for authentication state changes
 * @param {Function} callback - Function to call when auth state changes
 */
export function addAuthStateListener(callback) {
    state.listeners.push(callback);
}

/**
 * Remove a listener for authentication state changes
 * @param {Function} callback - Function to remove
 */
export function removeAuthStateListener(callback) {
    const index = state.listeners.indexOf(callback);
    if (index !== -1) {
        state.listeners.splice(index, 1);
    }
}

/**
 * Notify all listeners of auth state change
 * @param {boolean} isLoggedIn - New login status
 */
function notifyListeners(isLoggedIn) {
    state.listeners.forEach(callback => {
        try {
            callback(isLoggedIn, state.user);
        } catch (error) {
            console.error('Error in auth state listener:', error);
        }
    });
}

/**
 * Get current authentication state
 * @returns {Object} Current auth state
 */
export function getAuthState() {
    return {
        isLoggedIn: state.isLoggedIn,
        user: state.user
    };
}

/**
 * Check if user is currently logged in
 * @returns {boolean} True if logged in
 */
export function isLoggedIn() {
    return state.isLoggedIn;
}

/**
 * Get current user data
 * @returns {Object|null} User data or null if not logged in
 */
export function getCurrentUser() {
    return state.user;
}
```

#### 5.2 Update Main App Module
**Files:** `frontend/js/modules/app.js`

```javascript
// Add auth state import
import * as authState from './auth-state.js';

// In the init function, add:
authState.init();

// Set up auth state listener to sync UI
authState.addAuthStateListener((isLoggedIn, user) => {
    updateUserButtonState(isLoggedIn);
    
    // Optionally update other UI elements based on auth state
    const authDependentElements = document.querySelectorAll('[data-auth-required]');
    authDependentElements.forEach(element => {
        element.style.display = isLoggedIn ? 'block' : 'none';
    });
});
```

#### 5.3 Enhanced Authentication Tab Switching
**Files:** `frontend/js/modules/auth-tabs.js`

```javascript
/**
 * Authentication Page Tab Switching
 * 
 * Handles tab switching on the authentication page
 */

// Module state
const state = {
    initialized: false,
    activeTab: 'login'
};

/**
 * Initialize auth tabs functionality
 */
export function init() {
    if (state.initialized) {
        console.log('Auth tabs already initialized');
        return;
    }

    console.log('Initializing auth tabs');

    setupTabSwitching();
    
    state.initialized = true;
}

/**
 * Set up tab switching functionality
 */
function setupTabSwitching() {
    document.addEventListener('click', (event) => {
        const tabButton = event.target.closest('.auth-tab');
        if (tabButton) {
            const tabName = tabButton.dataset.tab;
            if (tabName) {
                switchToTab(tabName);
            }
        }
    });
}

/**
 * Switch to a specific tab
 * @param {string} tabName - Name of the tab to switch to
 */
function switchToTab(tabName) {
    // Update active tab button
    const allTabs = document.querySelectorAll('.auth-tab');
    allTabs.forEach(tab => {
        tab.classList.toggle('auth-tab--active', tab.dataset.tab === tabName);
    });

    // Update active tab content
    const allContent = document.querySelectorAll('.auth-tab-content');
    allContent.forEach(content => {
        content.classList.toggle('auth-tab-content--active', content.id === `${tabName}-tab`);
    });

    state.activeTab = tabName;
    console.log(`Switched to ${tabName} tab`);
}

/**
 * Get current active tab
 * @returns {string} Active tab name
 */
export function getActiveTab() {
    return state.activeTab;
}
```

### Phase 6: Enhanced Error Handling and Validation (Priority 3)

#### 6.1 Add Comprehensive Error Handling
**Files:** `internal/handlers/auth_handler.go`

```go
// Enhanced error handling for authentication
func (ah *AuthHandler) handleAuthError(w http.ResponseWriter, r *http.Request, message string, err error, statusCode int) {
    logging.L().Error("Authentication error", 
        "message", message, 
        "error", err, 
        "path", r.URL.Path, 
        "method", r.Method,
        "isHTMX", r.Header.Get("HX-Request") == "true")

    if r.Header.Get("HX-Request") == "true" {
        // For HTMX requests, return error content that can be swapped
        w.Header().Set("Content-Type", "text/html")
        w.WriteHeader(statusCode)
        w.Write([]byte(fmt.Sprintf(`
            <div class="auth-error" role="alert">
                <h3>Authentication Error</h3>
                <p>%s</p>
                <button onclick="location.reload()" class="button button--secondary">
                    Try Again
                </button>
            </div>
        `, message)))
        return
    }

    // For regular requests, use the standard error handling
    ah.handleError(w, r, message, err, statusCode)
}
```

#### 6.2 Add Session Validation on Every Request
**Files:** `internal/middleware/middleware.go`

```go
// Enhanced session validation
func (mw *Middleware) validateSession(r *http.Request) (*models.User, string, error) {
    cookie, err := r.Cookie(auth.SessionCookieName)
    if err != nil {
        return nil, "", errors.New("no session cookie")
    }

    userID, csrfToken, err := mw.SessionManager.GetSessionData(r.Context(), cookie.Value)
    if err != nil {
        return nil, "", err
    }

    user, err := mw.UserService.GetUserByID(r.Context(), userID)
    if err != nil {
        return nil, "", err
    }

    // Validate user is still active
    if !user.IsActive {
        return nil, "", errors.New("user account is disabled")
    }

    return user, csrfToken, nil
}
```

## Testing Plan

### 1. Unit Tests
- **Session Manager Tests**: Test session creation, validation, and expiry
- **Authentication Handler Tests**: Test login/signup flows with various inputs
- **Middleware Tests**: Test session validation and user context injection

### 2. Integration Tests
- **Complete Auth Flow**: Test signup → login → session persistence → logout
- **Database Integrity**: Verify password_hash preservation across all operations
- **HTMX Integration**: Test all HTMX authentication interactions

### 3. E2E Tests
- **Browser Session Persistence**: Test login state across page refreshes and browser restarts
- **Navigation Flow**: Test user menu → auth page → login → user management flow
- **Error Scenarios**: Test network failures, invalid credentials, session expiry

### 4. Manual Testing Checklist
- [ ] Login with valid credentials preserves session across app refresh
- [ ] Clicking app logo refreshes content but maintains login status
- [ ] User menu navigates to full-page interfaces instead of showing dropdown
- [ ] Authentication page provides smooth tab switching between login/signup
- [ ] User management page displays correct user information
- [ ] Logout properly clears session and redirects to home
- [ ] Invalid login attempts show appropriate error messages
- [ ] Session expiry gracefully redirects to authentication page

## CSS/SCSS Requirements

### New Components to Style
1. **Auth Page Layout** (`.auth-page`, `.auth-container`)
2. **Auth Tabs** (`.auth-tab`, `.auth-tab-content`)
3. **User Management Page** (`.user-manage-page`, `.user-section`)
4. **Header Logo Button** (update `.header__logo` for button styling)
5. **Error States** (`.auth-error`, `.auth-success-message`)

### Responsive Considerations
- Auth page should work well on mobile devices
- Tab switching should be touch-friendly
- User management page should adapt to different screen sizes

## Security Considerations

1. **Session Security**: Implement session fixation protection
2. **CSRF Protection**: Ensure all forms include CSRF tokens
3. **XSS Prevention**: Sanitize all user inputs in templates
4. **Session Hijacking**: Implement IP and User-Agent validation
5. **Password Security**: Ensure bcrypt hashing parameters are secure

## Performance Considerations

1. **Session Storage**: Monitor Redis performance with increased session operations
2. **Template Caching**: Ensure new templates are properly cached in production
3. **HTMX Requests**: Minimize payload sizes for auth-related requests
4. **Database Queries**: Optimize user lookup queries

## Rollback Plan

If issues arise during implementation:

1. **Phase 1 Rollback**: Revert to HX-Redirect temporarily while fixing issues
2. **Phase 2 Rollback**: Disable session refresh functionality
3. **Phase 3 Rollback**: Restore dropdown menu functionality
4. **Database Rollback**: Use database backups to restore corrupted password hashes

## Implementation Priority

1. **Week 1**: Phase 1 (Critical Authentication Fixes)
2. **Week 2**: Phase 2 (Session Persistence)
3. **Week 3**: Phase 3 (Full-Page Navigation)
4. **Week 4**: Phase 4-6 (Enhancements and Testing)

## Success Metrics

- [ ] Zero instances of password_hash corruption in logs
- [ ] 100% session persistence across app refreshes
- [ ] User feedback confirms improved authentication UX
- [ ] No HX-Redirect related page reloads in authentication flow
- [ ] All E2E tests passing consistently

---

**Next Steps**: Begin implementation with Phase 1 critical fixes, starting with the HX-Redirect removal and UpdateLastLogin database integrity fix.