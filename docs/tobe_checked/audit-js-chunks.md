# JavaScript Code Splitting Audit

## Overview

This document provides an analysis of the current JavaScript code organization and recommends improvements for optimized code splitting using dynamic imports.

## Current Structure

The application's JavaScript is organized as follows:

- **Entry Point**: `app.js` - Main application entry point that statically imports all core modules
- **Core Modules**: In the `modules/` directory (event handlers, filters, etc.)
- **Utility Modules**: In the `utils/` directory (DOM helpers, HTTP utilities)

## Current Import Patterns

### Mixed Import Strategies

The codebase currently uses a mix of static and dynamic imports:

1. **Static Imports** - Used in most cases:
   ```javascript
   import { setupInfiniteScroll } from './modules/infinite_scroll.js';
   import { registerGlobalEventHandlers } from './modules/event_handlers.js';
   ```

2. **Dynamic Imports** - Used occasionally:
   ```javascript
   // In infinite_scroll.js
   import('./filter_state.js').then(module => {
     const { getCurrentFilterStateFromDOM } = module;
     // Use the imported function
   });
   ```

### Identified Issue

A specific issue was identified where `filter_state.js` is being imported in two different ways:

- **Dynamically** in `infinite_scroll.js` using `import()`
- **Statically** in `event_handlers.js` using standard `import`

This prevents Vite from properly code-splitting this module, resulting in the warning:

```
/frontend/js/modules/filter_state.js is dynamically imported by /frontend/js/modules/infinite_scroll.js 
but also statically imported by /frontend/js/modules/event_handlers.js, 
dynamic import will not move module into another chunk.
```

## Recommended Code Splitting Strategy

### 1. Core vs. Feature-Specific Code

Divide the codebase into:

- **Core** (loaded immediately): Essential functionality needed on every page
- **Features** (loaded on demand): Functionality only needed in specific scenarios

### 2. Import Pattern Recommendations

#### A. Immediate Loading (Static Imports)

Use static imports for core functionality that should be loaded immediately:

```javascript
// In app.js
import { registerGlobalEventHandlers } from './modules/event_handlers.js';
import { setupCore } from './modules/core.js';
```

#### B. On-Demand Loading (Dynamic Imports)

Use dynamic imports for:
- Feature-specific code
- Rarely used functionality
- Large dependencies

```javascript
// Example of on-demand loading
document.querySelector('.filter-button').addEventListener('click', async () => {
  const { setupFilters } = await import('./modules/filters.js');
  setupFilters();
});
```

### 3. Route-Based Code Splitting

Load page-specific code based on the current route or page:

```javascript
// Detect current page and load appropriate modules
function loadPageModules() {
  const page = document.body.dataset.page;
  
  if (page === 'product-list') {
    import('./modules/product-list.js').then(module => {
      module.initProductList();
    });
  } else if (page === 'product-detail') {
    import('./modules/product-detail.js').then(module => {
      module.initProductDetail();
    });
  }
}
```

## Specific Module Recommendations

### Immediate Loading (Core)

These modules should be loaded immediately with static imports:

- `event_handlers.js` - Core event handling
- Core UI utilities in `utils/dom.js`
- `htmx-setup.js` - HTMX configuration

### On-Demand Loading (Features)

These modules should be loaded on demand with dynamic imports:

- `filters.js` (and related filter modules) - Only needed when filters are used
- `infinite_scroll.js` - Only needed on pages with infinite scroll
- `sidebar_toggle.js` - Only needed when sidebar is present
- `search.js` - Only needed when search functionality is used

### Fix for Current Warning

To fix the warning about `filter_state.js`:

1. **Option A**: Convert all imports to static (if this module is always needed)
   ```javascript
   // In infinite_scroll.js - Replace dynamic import with static
   import { getCurrentFilterStateFromDOM } from './filter_state.js';
   ```

2. **Option B**: Convert all imports to dynamic (preferred for code splitting)
   ```javascript
   // In event_handlers.js - Replace static import with dynamic
   async function handleGlobalClicks(event) {
     const { getCurrentFilterStateFromDOM } = await import('./filter_state.js');
     // Rest of the function
   }
   ```

## Implementation Plan

1. **Identify Core Modules**: Determine which modules are essential for initial page load
2. **Refactor Entry Point**: Update `app.js` to only statically import core modules
3. **Add Dynamic Imports**: Convert feature-specific modules to use dynamic imports
4. **Add Loading Triggers**: Implement event-based or route-based dynamic loading
5. **Fix Import Consistency**: Ensure each module is imported consistently (all static or all dynamic)

## Testing Recommendations

After implementing these changes:

1. Monitor network activity to verify modules are loaded only when needed
2. Check bundle sizes to ensure proper code splitting
3. Verify all functionality works correctly with the new loading strategy
4. Test performance on slower connections to validate improvements

## Conclusion

Adopting a consistent dynamic import strategy for feature-specific code will improve initial load times while maintaining all functionality. The key is to be consistent with how each module is imported - either always statically or always dynamically.