# Detailed UI Implementation Plan

This document provides detailed technical plans for implementing UI enhancements as outlined in Phase 3.5 of `docs/htmx/htmx_refactoring_plan_and_tracking.md` and inspired by `docs/UI/UI PWA.md`. Each task from P3.5.x will have a section here outlining its implementation strategy before development begins.

## P3.5.1 Implement Header Favorites Button

1.  **Task ID & Title**: P3.5.1 Implement Header Favorites Button
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Make the header favorites button (heart icon) functional.
    *   Clicking it should display the user\'s list of favorited products.
    *   Leverage the existing `/products` endpoint and its filtering capabilities.
    *   Move this icon to the footer, right of the filetr icon. Left of the aboslute discount button. Remove it from the header.
3.  **Affected Files/Components**:
    *   **Go Handlers**: `internal/handlers/frontend_handler.go` (specifically `HandleProducts` and how it uses `getFilterStateFromRequest`), `internal/handlers/base_handler.go` (potentially `getFilterStateFromRequest` if a new filter type is needed).
    *   **HTML Templates**: `frontend/templates/layouts/base.html` (for the header button), `frontend/templates/partials/_product_list.html` (to display the results).
    *   **Models**: `internal/models/filter_state.go` (if a new filter type like "favorites" is added).
    *   **Service/Repository**: `ProductService` / `ProductRepository` (to ensure they can handle filtering by a list of user-favorite product IDs).
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   No new endpoint. Will use existing `GET /products`.
    *   **Key Go Structs/Logic**:
        *   `BaseHandler.getFilterStateFromRequest`: Modify to recognize a new query parameter for favorites (e.g., `?view=favorites` or `?filter_by_user_favorites=true`).
        *   `models.FilterState`: If `filter_by_user_favorites=true` is present, `FilterState` should be populated accordingly. This might involve fetching the current user\'s favorite product IDs and storing them in `FilterState` to be used by `ToDBFilters()`.
        *   `ProductService.GetProductsSorted`: The `dbFilters` map generated from `FilterState.ToDBFilters()` will need to include a condition like `product_id IN (...)` if favorites are active. This requires user authentication to fetch user-specific favorite IDs.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `base.html` for the header button)**:
        ```html
        <a href="/products?view=favorites" <!-- Or chosen param, e.g., ?filter_by_user_favorites=true -->
           id="header-favorites-button"
           class="header-icon" <!-- Add appropriate classes -->
           hx-get="/products?view=favorites" <!-- Match backend param -->
           hx-target="#product-list-container"
           hx-swap="innerHTML"
           hx-push-url="true"
           hx-indicator="#global-loading-spinner" <!-- Or appropriate indicator -->
           aria-label="View Favorites">
            <svg><!-- heart icon SVG --></svg>
        </a>
        ```
    *   **HTMX Attributes**: As listed above.
    *   **JavaScript**: None anticipated for core functionality.
    *   **CSS Styling**: Ensure consistent styling with other header icons. Provide visual feedback for active/focus states.
6.  **Key Considerations / Open Questions**:
    *   User Authentication: Backend needs to identify the logged-in user to fetch their specific favorites.
    *   Empty State: If the user has no favorites, `_product_list.html` should render a clear message within the `#product-list-container`.
    *   Interaction with Other Filters: Define behavior. Does clicking "Favorites" clear other active filters/search terms, or combine? Initial assumption: It acts as a primary filter, potentially overriding others or being an exclusive view. The assumption is correct. It replaces all filtering. The filetr chips should  all close and just a chip "favorites" should be visible
7.  **Testing Focus**:
    *   Button click updates product list to show only the logged-in user\'s favorited items.
    *   URL correctly reflects the favorites view.
    *   Handles users with no favorites (shows empty state message).
    *   Works for logged-out users (links to login; this is the preferred way, maybe a message and the choice to login/sign on or go back to the previvous view).

## P3.5.2 Implement Product Card Favorite Toggle

1.  **Task ID & Title**: P3.5.2 Implement Product Card Favorite Toggle
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Allow users to add/remove products from their favorites list by clicking a heart icon on individual product cards.
    *   The icon on the card must visually reflect the current favorite status. it should turn red when selected, and transparant when not selected
3.  **Affected Files/Components**:
    *   **Go Handlers**: New handler method, e.g., in `UserInteractionHandler` or a dedicated `FavoritesHandler`, like `HandleToggleFavoriteProduct`.
    *   **HTML Templates**: `frontend/templates/partials/_product_card.html`.
    *   **Service/Repository**: Service layer to interact with the database for adding/removing user favorites.
    *   **CSS**: Styles for filled vs. empty heart icon.
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   New: `POST /user/favorites/product/{productID}/toggle`
        *   Method: `POST` (state-changing).
        *   Path Parameter: `productID`.
        *   Purpose: Toggles the favorite status of `productID` for the currently authenticated user.
    *   **Key Go Structs/Logic**:
        *   `HandleToggleFavoriteProduct(w http.ResponseWriter, r *http.Request)`:
            *   Extract `productID`. Get `userID` from session/auth.
            *   Call `favoritesService.ToggleFavorite(userID, productID)`. This service method returns the new favorite status (e.g., boolean `isFavorite`).
            *   The handler should then re-render just the favorite button part of the product card with the updated status and send it back.
            *   The `_product_card.html` template (or a new sub-partial for just the button) will need a parameter for the product\'s current favorite state.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `_product_card.html`)**:
        The product card data passed to the template must include `IsFavorite bool` for the current user and product.
        ```html
        <button class="favorite-toggle-button {{if .Product.IsFavorite}}is-favorite{{end}}"
                hx-post="/user/favorites/product/{{.Product.ID}}/toggle"
                hx-target="this"
                hx-swap="outerHTML" <!-- Swaps the button itself with the new version from server -->
                aria-pressed="{{if .Product.IsFavorite}}true{{else}}false{{end}}"
                aria-label="{{if .Product.IsFavorite}}Remove from favorites{{else}}Add to favorites{{end}}">
            <!-- SVG icon here, potentially dynamic based on .Product.IsFavorite if not handled by CSS alone -->
            <svg class="icon-heart {{if .Product.IsFavorite}}filled{{else}}empty{{end}}"> <!-- ... SVG paths ... --></svg>
        </button>
        ```
    *   **HTMX Attributes**: As listed.
    *   **JavaScript**: None for core functionality.
    *   **CSS Styling**:
        *   Define styles for `.favorite-toggle-button svg.empty` and `.favorite-toggle-button svg.filled` (or `.favorite-toggle-button.is-favorite svg`).
6.  **Key Considerations / Open Questions**:
    *   Error Handling: If backend toggle fails (DB error, not authenticated), return an appropriate HTTP error. HTMX can handle this via `htmx:responseError` event or by swapping an error message.
    *   State Synchronization: If the main product list is currently showing "Favorites" (from P3.5.1), toggling a favorite on a card should ideally trigger a refresh of that list. This can be achieved using an `HX-Trigger` response header from the toggle endpoint, which the main list container can listen for. Example: `HX-Trigger: refreshFavoritesList`.
7.  **Testing Focus**:
    *   Clicking button correctly adds/removes product from user\'s favorites in the DB.
    *   Button icon updates instantly on the card after server response.
    *   `aria-pressed` and `aria-label` attributes update correctly.
    *   If favorites list is visible and `HX-Trigger` is used, it refreshes.
    *   Graceful handling for non-authenticated users (button disabled or prompts login).

---

## P3.5.3 Implement Header Cart Button (Initial View)

1.  **Task ID & Title**: P3.5.3 Implement Header Cart Button (Initial View)
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Make the header cart button functional.
    *   Clicking it should display the user's list of products currently in their cart.
    *   Leverage the existing `/products` endpoint and its filtering capabilities, similar to favorites.
    *   "To start it, just show the products in the cart, which are selected in the product cards, completely the same as favorites." IN the future we might enhencade this with dedicated product-cards for exporting shopping lists etc, for now just the filter function
3.  **Affected Files/Components**:
    *   **Go Handlers**: `internal/handlers/frontend_handler.go` (`HandleProducts`), `internal/handlers/base_handler.go` (`getFilterStateFromRequest`).
    *   **HTML Templates**: `frontend/templates/layouts/base.html` (header button), `frontend/templates/partials/_product_list.html` (results display).
    *   **Models**: `internal/models/filter_state.go` (to handle cart view).
    *   **Service/Repository**: `ProductService` / `ProductRepository` (to handle filtering by user's carted product IDs). `CartService` (to get carted product IDs).
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   No new endpoint. Will use existing `GET /products`.
    *   **Key Go Structs/Logic**:
        *   `BaseHandler.getFilterStateFromRequest`: Modify to recognize a new query parameter for cart view (e.g., `?view=cart` or `?filter_by_user_cart=true`).
        *   `models.FilterState`: If `filter_by_user_cart=true`, fetch current user's carted product IDs (via a `CartService`) and store them in `FilterState` for `ToDBFilters()`.
        *   `ProductService.GetProductsSorted`: `dbFilters` will include `product_id IN (...)` for carted items. Requires user authentication.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `base.html` for header button)**:
        ```html
        <a href="/products?view=cart" <!-- Or chosen param -->
           id="header-cart-button"
           class="header-icon"
           hx-get="/products?view=cart" <!-- Match backend param -->
           hx-target="#product-list-container"
           hx-swap="innerHTML"
           hx-push-url="true"
           hx-indicator="#global-loading-spinner"
           aria-label="View Cart">
            <svg><!-- cart icon SVG --></svg>
        </a>
        ```
    *   **HTMX Attributes**: As listed above.
    *   **JavaScript**: None anticipated for core functionality.
    *   **CSS Styling**: Consistent styling with other header icons. Active/focus states.
6.  **Key Considerations / Open Questions**:
    *   User Authentication: Required to fetch user-specific cart.
    *   Empty State: `_product_list.html` should show a message if the cart is empty.
    *   Interaction with Other Filters: Similar to favorites, clicking "Cart" should exclusively show cart items, clearing other filters and search terms. Filter chips should update accordingly (e.g., only a "Cart" chip visible).
7.  **Testing Focus**:
    *   Button click updates product list to show only cart items for the logged-in user.
    *   URL reflects cart view.
    *   Handles empty cart state.
    *   Logged-out users: Button hidden/disabled or links to login.

## P3.5.4 Implement Product Card Add to Cart Toggle

1.  **Task ID & Title**: P3.5.4 Implement Product Card Add to Cart Toggle
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Allow users to add/remove products from their shopping cart by clicking an icon on product cards.
    *   Icon on the card must visually reflect if the item is in the cart.
3.  **Affected Files/Components**:
    *   **Go Handlers**: New handler method in `UserInteractionHandler` or a dedicated `CartHandler`, e.g., `HandleToggleCartItem`.
    *   **HTML Templates**: `frontend/templates/partials/_product_card.html`.
    *   **Service/Repository**: `CartService` to manage cart items in the database.
    *   **CSS**: Styles for in-cart vs. not-in-cart icon.
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   New: `POST /user/cart/product/{productID}/toggle`
        *   Method: `POST`.
        *   Path Parameter: `productID`.
        *   Purpose: Toggles cart status of `productID` for the current authenticated user.
    *   **Key Go Structs/Logic**:
        *   `HandleToggleCartItem(w http.ResponseWriter, r *http.Request)`:
            *   Extract `productID`. Get `userID` from session/auth.
            *   Call `cartService.ToggleItem(userID, productID)`, returning new cart status (`isInCart`).
            *   Handler re-renders the cart toggle button partial with updated status.
            *   Product card template data needs `Product.IsInCart bool`.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `_product_card.html`)**:
        ```html
        <button class="cart-toggle-button {{if .Product.IsInCart}}is-in-cart{{end}}"
                hx-post="/user/cart/product/{{.Product.ID}}/toggle"
                hx-target="this"
                hx-swap="outerHTML"
                aria-pressed="{{if .Product.IsInCart}}true{{else}}false{{end}}"
                aria-label="{{if .Product.IsInCart}}Remove from cart{{else}}Add to cart{{end}}">
            <svg class="icon-cart {{if .Product.IsInCart}}filled{{else}}empty{{end}}"> <!-- ... SVG paths for cart icon ... --></svg>
        </button>
        ```
    *   **HTMX Attributes**: As listed.
    *   **JavaScript**: None for core functionality.
    *   **CSS Styling**: Define styles for cart icon (e.g., filled/empty states, or color changes).
6.  **Key Considerations / Open Questions**:
    *   Error Handling: Backend failure (DB error, not authenticated) should return HTTP error.
    *   State Synchronization: If cart view (P3.5.3) is active, toggling an item should trigger its refresh using `HX-Trigger: refreshCartList`.
7.  **Testing Focus**:
    *   Button click adds/removes product from user's cart in DB.
    *   Icon on card updates instantly.
    *   `aria-pressed` and `aria-label` update.
    *   If cart list is visible and `HX-Trigger` is used, it refreshes.
    *   Graceful handling for non-authenticated users.

## P3.5.5 Implement Header Account Button (Placeholder Page)

1.  **Task ID & Title**: P3.5.5 Implement Header Account Button (Placeholder Page). Implementing user acounts and subscriptions are catered for in the backend, But for now just a placeholder will do. We will define a complete new planning for this later.
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Provide access to user settings, login, filter defaults, payment info.
    *   "Shown in the main viewport. Put in some placeholders for now."
3.  **Affected Files/Components**:
    *   **Go Handlers**: `internal/handlers/frontend_handler.go`.
    *   **HTML Templates**: `frontend/templates/layouts/base.html` (header button), new `frontend/templates/pages/account_placeholder.html`.
    *   **Routing**: `cmd/server/routes.go`.
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   New: `GET /account`
    *   **Key Go Structs/Logic**:
        *   `FrontendHandler.HandleAccountPage(w http.ResponseWriter, r *http.Request)`:
            *   Renders `account_placeholder.html`.
            *   Passes standard base template data.
            *   May require user to be authenticated to access.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `base.html` for header button)**:
        ```html
        <a href="/account" id="header-account-button" class="header-icon" aria-label="Account Settings">
            <svg><!-- account/user icon SVG --></svg>
        </a>
        ```
    *   **HTML (New: `account_placeholder.html`)**:
        ```html
        {{template "layouts/base" .}}
        {{define "title"}}Account Settings{{end}}
        {{define "content"}}
        <div class="container">
            <h1>Account Settings</h1>
            <p>This page is under construction. Future settings for your profile, preferences, and payment info will appear here.</p>
            <!-- Placeholder for login/logout if applicable -->
        </div>
        {{end}}
        ```
    *   **HTMX Attributes**: None for simple link navigation.
    *   **JavaScript**: None.
    *   **CSS Styling**: Basic page styling.
6.  **Key Considerations / Open Questions**:
    *   Authentication: If `/account` requires login, redirect to login page if user is not authenticated.
    *   Content: Basic placeholder text is sufficient for now.
7.  **Testing Focus**:
    *   Button in header links to `/account`.
    *   Placeholder page displays correctly.
    *   Authentication check (if implemented).

## P3.5.6 Implement Header Information Button

1.  **Task ID & Title**: P3.5.6 Implement Header Information Button
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   "Shows the home page, later to become the default when opening the app if someone is not logged in. For now just the homepage behind it. Use the information icon".
3.  **Affected Files/Components**:
    *   **HTML Templates**: `frontend/templates/layouts/base.html` (header button).
4.  **Backend Implementation Details**:
    *   No backend changes needed. The `/` route (homepage) is already handled.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `base.html` for header button)**:
        ```html
        <a href="/" id="header-info-button" class="header-icon" aria-label="Information - Home">
            <svg><!-- information icon SVG --></svg>
        </a>
        ```
    *   **HTMX Attributes**: None.
    *   **JavaScript**: None.
    *   **CSS Styling**: Consistent styling.
    I think you allready hevae the info-icon.svg which you can use for this, just replace the circle for this one and you are ok.
6.  **Key Considerations / Open Questions**:
    *   Icon choice for "information".
7.  **Testing Focus**:
    *   Button in header links to the homepage (`/`).

## P3.5.7 Style Footer Filter Button

1.  **Task ID & Title**: P3.5.7 Style Footer Filter Button
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   "Open the sidebar. Completed technically, but not yet properly styled. Some css work to do to improve the looks".
3.  **Affected Files/Components**:
    *   **SCSS Files**: e.g., `frontend/scss/layout/_footer.scss`, `frontend/scss/components/_buttons.scss`.
4.  **Backend Implementation Details**: None.
5.  **Frontend Implementation Details**:
    *   **HTML Structure**: Existing button structure is assumed to be functional for opening the sidebar.
    *   **CSS Styling**:
        *   Review current styling.
        *   Apply desired visual improvements: padding, margins, icon sizing/alignment, text styling, background, border.
        *   Ensure hover, focus, and active states are clear.
        *   Consider light/dark mode compatibility.
6.  **Key Considerations / Open Questions**:
    *   Specific design requirements/mockups for the improved look.
    *   Accessibility: Ensure sufficient contrast, focus indicators.
7.  **Testing Focus**:
    *   Visual appearance matches desired styling.
    *   Button remains functional.
    *   Good usability and accessibility.

## P3.5.8 Implement Product Card Info Button (Placeholder)

1.  **Task ID & Title**: P3.5.8 Implement Product Card Info Button (Placeholder)
2.  **Objective** (from `docs/UI/UI PWA.md`):
    *   Button on product card to show detailed single product view.
    *   "Replaces product list with detailed single product view".
    *   "For now show a placeholder card telling the user that the feature is to be implemented."
    *   "Nice to have: product card turns around, animation." (Animation deferred).
3.  **Affected Files/Components**:
    *   **Go Handlers**: `internal/handlers/frontend_handler.go`.
    *   **HTML Templates**: `frontend/templates/partials/_product_card.html` (button), new `frontend/templates/partials/_product_detail_placeholder.html`.
    *   **Routing**: `cmd/server/routes.go`.
4.  **Backend Implementation Details**:
    *   **Endpoints**:
        *   New: `GET /product/{productID}/details-placeholder` (or similar, to avoid conflict with future full details page).
    *   **Key Go Structs/Logic**:
        *   `FrontendHandler.HandleProductDetailPlaceholder(w http.ResponseWriter, r *http.Request)`:
            *   Extract `productID` from path.
            *   Fetch minimal product data (e.g., Name, ID) using `productService`.
            *   Render `_product_detail_placeholder.html` partial, passing product data.
5.  **Frontend Implementation Details**:
    *   **HTML Structure (in `_product_card.html`)**:
        ```html
        <button class="info-button"
                hx-get="/product/{{.Product.ID}}/details-placeholder"
                hx-target="#product-list-container" <!-- As per requirement to replace product list -->
                hx-swap="innerHTML"
                hx-push-url="/products/{{.Product.ID}}/details" <!-- Future-friendly URL -->
                aria-label="More information about {{.Product.Name}}">
            <svg><!-- info icon SVG --></svg>
        </button>
        ```
    *   **HTML (New: `_product_detail_placeholder.html`)**:
        ```html
        <!-- This is a partial, meant to be swapped into #product-list-container -->
        <div class="product-detail-placeholder">
            <h2>Product Details: {{.Product.Name}}</h2>
            <p>More detailed information for this product, including price history and comparable products, is coming soon!</p>
            <p><a href="/products" hx-get="/products" hx-target="#product-list-container" hx-swap="innerHTML" hx-push-url="/products">Back to product list</a></p>
        </div>
        ```
    *   **HTMX Attributes**: As listed.
    *   **JavaScript**: None for placeholder.
    *   **CSS Styling**: Basic styling for the placeholder message.
6.  **Key Considerations / Open Questions**:
    *   URL for `hx-push-url`: Should be the intended final URL for the actual detail page.
    *   "Back to product list" link: Important for navigation from the placeholder. It should reload the default product list.
7.  **Testing Focus**:
    *   Info button on product card loads the placeholder content into `#product-list-container`.
    *   URL updates via `hx-push-url`.
    *   Placeholder displays product name correctly.
    *   "Back to product list" link works.

## P3.5.9 Realign/Replace Header Test Component Buttons

1.  **Task ID & Title**: P3.5.9 Realign/Replace Header Test Component Buttons. It is not about replacing or realigining but just remove those testing components. The icons remain in use.
2.  **Objective**: Review and remove or relocate the existing header test buttons (`/componentX` links) as their functionality is superseded by actual UI elements or integrated into the `/products` endpoint. So the only task is to clean the components from the backend and frontend. The icons are actually going to be used for different pruposes. So do not delete buttons!
3.  **Affected Files/Components**:
    *   **HTML Templates**: `frontend/templates/layouts/base.html` (header).
    *   **Go Handlers**: `internal/handlers/frontend_handler.go` (remove `HandleComponentX` methods).
    *   **Routing**: `cmd/server/routes.go` (unregister routes for `/componentX`).
4.  **Implementation Details**:
    *   **Step 1: Audit Existing Test Buttons & Handlers**:
        *   Identify all buttons in `base.html` that link to `/component...` URLs.
        *   Map them to their respective `HandleComponentX` methods in `frontend_handler.go`.
        *   Document the specific functionality each button/handler was testing (e.g., specific filter, sort, search term via `/products`).
    *   **Step 2: Verify Functionality Coverage**:
        *   For each test button, confirm if its tested functionality is now achievable through the new embedded UI controls (sidebar filters, header search, sort buttons) and the main `/products` endpoint.
    *   **Step 3: Removal**:
        *   Remove the HTML for the test buttons from `base.html`.
        *   Delete the corresponding `HandleComponentX` methods from `frontend_handler.go`.
        *   Remove the route registrations for these handlers in `cmd/server/routes.go`.
    *   **Example Test Buttons to Review (based on previous P2.3, P2.4, P2.5)**:
        *   "Jumbo /component1": Tested a specific filter. Likely covered by sidebar.
        *   "Search Gehakt /component2": Tested search. Covered by header search.
        *   "Price Sort /component3": Tested sorting. Covered by sidebar sort.
        *   "Home /component4": Tested loading homepage/default products. Covered by logo/info button.
5.  **Key Considerations / Open Questions**:
    *   Ensure no unique test scenarios are lost if they aren't covered by the new UI. For this phase, the assumption is that the primary goal is to clean up the main user-facing header. You may deleted the test scenarios. 
6.  **Testing Focus**:
    *   Verify test buttons are removed from the UI. This is not good! Those button do get a new purpose, the heart, the cart and the person are going to be used. The circle is going to be a info icon.
    *   Verify that accessing old `/componentX` URLs results in a 404 Not Found. Delete them
    *   Confirm that the functionalities previously tested by these buttons are still working through the new UI elements., NO not needed!

## P3.5.10 Plan User Preferences for Filters & Sort

1.  **Task ID & Title**: P3.5.10 Plan User Preferences for Filters & Sort
2.  **Objective** (from `docs/UI/UI PWA.md`): Define how user-specific defaults for initial filters and sort order will be handled. This task is for *planning and documentation*, not immediate implementation.
3.  **Affected Files/Components**: This task will primarily result in documentation within this file (`detailed_ui_implementation_plan.md`) or a dedicated design document.
4.  **Planning Details**:
    *   **Data Storage**:
        *   Propose adding fields to the `User` model/database table. E.g.:
            *   `default_sort_preference TEXT` (e.g., "price_asc", "discount_abs_desc")
            *   `default_filter_preference JSONB` (e.g., `{"is_promotion": true, "shops": ["shopA", "shopB"]}`)
    *   **Backend Logic (`BaseHandler.getFilterStateFromRequest`)**:
        *   If a user is authenticated:
            *   After parsing URL query parameters, if certain parameters (like `sort`, or specific filters) are *not* present in the URL:
                *   Attempt to load the user's saved preferences for those missing parameters from the database.
                *   Apply these user preferences to the `models.FilterState`.
        *   If user is not authenticated, or has no saved preferences, or URL params are present (URL params take precedence):
            *   Apply application-wide default filters/sort (e.g., "promotions only", "sort by absolute discount").
    *   **UI for Setting Preferences**:
        *   This will be part of the future "Account Settings" page (P3.5.5). For now, just acknowledge that a UI will be needed there.
        *   Examples: Dropdown for default sort, checkboxes/multiselect for default shop filters, toggle for "promotions only" default.
    *   **Scope of Preferences**:
        *   Initial scope: Default sort order, default "promotions only" status, default selected shops.
        *   Future considerations: Default price range, default category filters.
    *   **Precedence**:
        1.  URL Query Parameters (always override everything).
        2.  User-Saved Preferences (if logged in and no overriding URL param).
        3.  Application-Wide Defaults.
5.  **Key Considerations / Open Questions**:
    *   Complexity of merging URL params with stored preferences.
    *   How to handle "clear all filters" when user defaults are active? Should it revert to user defaults or true application defaults? (Suggest: revert to user defaults if set, else app defaults).
    *   Initial population of preferences for existing users (e.g., null, or migrate from current app defaults).
6.  **Deliverable for this Task**: A documented section here outlining the proposed approach. No code implementation in this phase.
7.  **Testing Focus (for the plan itself)**:
    *   Clarity and completeness of the proposed preference system.
    *   Logical consistency of the precedence rules.
    *   Feasibility for future implementation. 