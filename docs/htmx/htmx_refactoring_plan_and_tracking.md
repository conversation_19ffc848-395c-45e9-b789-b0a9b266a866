## Known Issues

- The legacy `FilterHandler` test (`TestFilterHandler_StateFromRequest`) fails due to malformed URL construction and outdated filter logic. This is expected and will be addressed or removed as part of the planned handler refactor. No further effort will be spent fixing this test until the new handler is in place. 

- [x] Legacy `FilterHandler` test issue is now tracked as a known issue (see Known Issues section above). No further action until handler refactor.

// NEXT STEP:
- [ ] Continue with the next actionable item in the refactor plan (e.g., begin implementation or migration of new handler logic, or next prioritized task). 

---

## [2024-xx-xx] Legacy Filter Handler and Asset Hashing Cleanup Completed

- All legacy filter handler code, tests, and documentation have been removed.
- The codebase now uses only the new `/products` endpoint and URL parameters for filtering.
- All static asset management is handled by Vite and Go's static file serving, with no custom asset hashing logic.
- Documentation, plans, and tests are up to date and reflect the current architecture.

--- 